#!/usr/bin/env python3
"""
MANDOLIN PA AUTOMATION SYSTEM
An agent-based system to handle dynamic, conditional, and complex PA forms.
This system is designed to be universal and handle any PA form for any drug.
"""

import json
import fitz  # PyMuPDF
import base64
from pathlib import Path
import google.generativeai as genai
from dotenv import load_dotenv
import os
from typing import Dict, List, Optional, Any, Tuple, Literal
from dataclasses import dataclass, field
import datetime
import re
from PIL import Image
import io


load_dotenv()
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

# --- Data Structures ---

@dataclass
class FormField:
    """Represents a single fillable field on the PA form."""
    field_id: str
    page_num: int
    type: Literal["char_boxes", "underline", "checkbox"]
    neighboring_label: str
    coordinates: List[List[float]]  # Flexible for different types
    semantic_purpose: Optional[str] = None
    value: Optional[Any] = None

    def to_dict(self):
        return {
            "field_id": self.field_id,
            "page_num": self.page_num,
            "type": self.type,
            "neighboring_label": self.neighboring_label,
            "coordinates": self.coordinates,
            "semantic_purpose": self.semantic_purpose,
            "value": self.value,
        }

@dataclass
class FormSchema:
    """A structured representation of all fillable fields on the PA form."""
    fields: List[FormField]
    
    def to_dict(self):
        return {
            "fields": [field.to_dict() for field in self.fields]
        }

@dataclass
class ExtractedData:
    """Structured data extracted from referral documents, keyed by semantic purpose."""
    data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Correction:
    """Represents a single correction to be made to the form."""
    field_id: str
    semantic_purpose: str
    correct_value: Any
    reason: str

    def to_dict(self):
        return {
            "field_id": self.field_id,
            "semantic_purpose": self.semantic_purpose,
            "correct_value": self.correct_value,
            "reason": self.reason
        }

# --- Agent Components ---

class SchemaHarvesterAgent:
    """
    Analyzes a blank FLAT PA form to identify ALL fillable fields,
    their types, coordinates, and nearby labels. This is a complex,
    one-time visual analysis per form type.
    """
    def __init__(self):
        self.model = genai.GenerativeModel('gemini-1.5-pro-latest')

    def harvest_schema(self, pa_form_path: Path) -> FormSchema:
        """
        Analyzes the PA form image and generates a comprehensive schema of all fillable fields.
        """
        print(f"🌾 Activating Schema Harvester Agent for: {pa_form_path.name}")
        
        doc = fitz.open(pa_form_path)
        
        page_images_b64 = []
        for page in doc:
            # Use a higher DPI for better analysis
            mat = fitz.Matrix(300/72, 300/72)
            pix = page.get_pixmap(matrix=mat, alpha=False)
            img_data = pix.tobytes("png")
            page_images_b64.append(base64.b64encode(img_data).decode())
        
        doc.close()

        prompt = self._build_harvesting_prompt()
        
        model_input = [prompt]
        for img_b64 in page_images_b64:
            model_input.append({"mime_type": "image/png", "data": img_b64})
            
        print("🤖 Asking AI to perform detailed visual analysis to harvest form schema...")

        try:
            response = self.model.generate_content(model_input,
                generation_config=genai.types.GenerationConfig(
                    response_mime_type="application/json"
                )
            )
            
            form_fields_json = json.loads(response.text)
            form_fields = [FormField(**field_data) for field_data in form_fields_json.get("fields", [])]
            
            schema = FormSchema(fields=form_fields)
            print(f"✅ Schema harvested successfully with {len(schema.fields)} fields.")
            return schema

        except Exception as e:
            # This is a critical failure. It's better to stop than proceed with a bad schema.
            print(f"❌ CRITICAL ERROR: AI failed to create a valid form schema. Cannot proceed.")
            print(f"   Reason: {e}")
            # In a real system, this would raise an exception or alert a human.
            # For now, we exit to prevent further errors.
            exit(1)


    def _build_harvesting_prompt(self) -> str:
        """Constructs the detailed prompt for visual schema harvesting."""
        return """
You are a world-class document analysis AI. Your sole task is to analyze the provided images of a medical form and create a comprehensive JSON "map" of EVERY SINGLE fillable field.

**1. Goal & Field Types:**
Identify all of the following fillable field types:
- `char_boxes`: A sequence of individual boxes for single characters (e.g., for a name or ID number).
- `underline`: A simple horizontal line where a user would write text.
- `checkbox`: A small square box for a checkmark (e.g., for 'Yes'/'No' answers).

**2. Extraction Requirements per Field:**
For EACH field you find, you MUST extract the following information:
- `field_id`: Create a unique, human-readable ID in `snake_case` (e.g., `patient_last_name_boxes`, `drug_name_underline`).
- `page_num`: The 0-indexed page number where the field appears.
- `type`: The type of the field (`char_boxes`, `underline`, or `checkbox`).
- `neighboring_label`: The verbatim text of the label that is geographically closest to the field. This context is vital.
- `coordinates`: The bounding box coordinates for the field. ALL coordinates MUST be normalized (values between 0.0 and 1.0) relative to the page dimensions. The origin (0,0) is the TOP-LEFT corner.

**3. Coordinate Formatting Rules (VERY IMPORTANT):**
- For `underline`: A single bounding box `[[x1, y1, x2, y2]]` representing the line's area.
- For `checkbox`: A single bounding box `[[x1, y1, x2, y2]]` for the checkable square.
- For `char_boxes`: An array of individual bounding boxes, one for EACH character box. `[[x1, y1, x2, y2], [x1, y1, x2, y2], ...]`. You must provide coordinates for every single box in the sequence.

**4. Output Format:**
You MUST return your findings as a single, valid JSON object. Do not include any other text, apologies, or explanations.

**Example JSON Output:**
```json
{
  "fields": [
    {
      "field_id": "member_last_name_boxes",
      "page_num": 0,
      "type": "char_boxes",
      "neighboring_label": "Last name:",
      "coordinates": [
        [0.10, 0.25, 0.12, 0.27],
        [0.13, 0.25, 0.15, 0.27],
        [0.16, 0.25, 0.18, 0.27]
      ]
    },
    {
      "field_id": "drug_strength_underline",
      "page_num": 0,
      "type": "underline",
      "neighboring_label": "Drug strength:",
      "coordinates": [[0.10, 0.80, 0.45, 0.81]]
    },
    {
      "field_id": "clinical_question_yes_checkbox",
      "page_num": 1,
      "type": "checkbox",
      "neighboring_label": "Is the patient over 18?",
      "coordinates": [[0.75, 0.55, 0.77, 0.57]]
    }
  ]
}
```

Now, analyze the form images with extreme precision and return the JSON schema.
"""

class SemanticMapperAgent:
    """Takes a raw schema of fields and assigns meaningful semantic purposes."""

    def __init__(self):
        self.model = genai.GenerativeModel('gemini-1.5-pro-latest')

    def map_semantics(self, schema: FormSchema) -> FormSchema:
        print(f"🗺️  Activating Semantic Mapper Agent for {len(schema.fields)} fields.")
        if not schema.fields:
            return schema

        # Create a simplified list for the prompt
        field_contexts = [{"field_id": f.field_id, "label": f.neighboring_label} for f in schema.fields]
        
        prompt = self._build_prompt(field_contexts)
        
        try:
            response = self.model.generate_content(prompt)
            
            # Use regex to safely parse the response
            # Expected format: field_id: semantic_purpose
            mappings = dict(re.findall(r"(\S+):\s*(\S+)", response.text))
            
            for field in schema.fields:
                if field.field_id in mappings:
                    purpose = mappings[field.field_id]
                    field.semantic_purpose = purpose if purpose.lower() != 'null' else None

            print("✅ Semantic mapping complete.")
            return schema
        except Exception as e:
            print(f"❌ Semantic mapping failed: {e}")
            # Proceed with a partially mapped schema
            return schema

    def _build_prompt(self, field_contexts: list) -> str:
        """Builds the detailed prompt for the LLM."""
        
        # Create a string representation of the fields to be mapped
        context_str = "\n".join([f"- ID: `{f['field_id']}`, Label: '{f['label']}'" for f in field_contexts])

        return f"""
You are an expert in medical administration and data ontology. Your task is to analyze a list of field IDs and their associated labels from a Prior Authorization form and assign a precise, machine-readable 'semantic_purpose' to each one.

**Instructions:**
1.  Analyze the provided list of fields.
2.  For each field, use its ID and neighboring label to determine its real-world meaning.
3.  Create a `snake_case` version of this purpose. This will be used as a key to find patient data.
4.  If a label is a clinical question requiring a "Yes" or "No" answer, its purpose should reflect the question.
5.  If a field's purpose is ambiguous, cannot be determined, or is not a data entry field (e.g., a form title), assign `null`.

**Crucial: Good vs. Bad Examples**
*   ID: `member_last_name_boxes`, Label: 'Last name:' -> `patient_last_name` (GOOD)
*   ID: `form_title_header`, Label: 'HealthKeepers, Inc.' -> `null` (GOOD - This is a header, not a field)
*   ID: `weight_field`, Label: 'Weight in kilograms:' -> `patient_weight_kg` (GOOD)
*   ID: `age_question_checkbox`, Label: 'Is the member >= 18 years of age?' -> `clinical_question_is_patient_over_18` (GOOD)
*   ID: `prescriber_npi_boxes`, Label: 'NPI number:' -> `prescriber_npi` (GOOD)


**Output Format:**
Return ONLY the mappings as a list of `field_id: semantic_purpose` pairs, one per line. Do NOT include explanations.

**Example Output:**
member_last_name_boxes: patient_last_name
drug_strength_underline: drug_strength
clinical_question_yes_checkbox: clinical_question_is_patient_over_18
form_title_header: null

**Fields to Process:**
{context_str}
"""

class DataExtractionAgent:
    """Uses an LLM to extract structured data from referral documents based on a schema."""
    def __init__(self):
        self.model = genai.GenerativeModel('gemini-1.5-pro-latest')

    def extract_data(self, referral_path: Path, schema: FormSchema) -> ExtractedData:
        """Performs targeted data extraction based on the semantic purposes in the schema."""
        print(f"🔎 Activating Data Extraction Agent for: {referral_path.name}")

        if not schema.fields:
            print("⚠️ Warning: Form schema is empty. Skipping extraction.")
            return ExtractedData()
        
        # Filter for fields that have a semantic purpose
        required_data_points = {
            field.semantic_purpose: field.neighboring_label
            for field in schema.fields if field.semantic_purpose
        }
        
        if not required_data_points:
            print("⚠️ Warning: No fields with semantic purpose found. Skipping extraction.")
            return ExtractedData()

        # 1. Convert referral document to images for the LLM
        doc = fitz.open(referral_path)
        page_images_b64 = []
        for page in doc:
            mat = fitz.Matrix(2.0, 2.0)
            pix = page.get_pixmap(matrix=mat, alpha=False)
            img_data = pix.tobytes("png")
            page_images_b64.append(base64.b64encode(img_data).decode())
        doc.close()

        # 2. Build and execute the prompt
        prompt = self._build_extraction_prompt(required_data_points)
        model_input = [prompt]
        for img_b64 in page_images_b64:
            model_input.append({"mime_type": "image/png", "data": img_b64})

        print(f"🤖 Asking AI to extract {len(required_data_points)} data points...")

        try:
            response = self.model.generate_content(model_input,
                generation_config=genai.types.GenerationConfig(
                    response_mime_type="application/json"
                )
            )
            return self._parse_extraction_response(response.text)
        except Exception as e:
            print(f"❌ Data extraction failed: {e}")
            return ExtractedData()

    def _parse_extraction_response(self, response_text: str) -> ExtractedData:
        """Safely parses the JSON response from the LLM."""
        try:
            data = json.loads(response_text)
            print("✅ Data extraction successful.")
            return ExtractedData(data=data.get("extracted_data", {}))
        except json.JSONDecodeError as e:
            print(f"❌ Error decoding JSON from extraction response: {e}")
            print(f"   Raw response: {response_text}")
            return ExtractedData()

    def _build_extraction_prompt(self, required_data: Dict[str, str]) -> str:
        """Builds a targeted prompt for data extraction."""
        
        data_points_str = "\n".join([f'- `{key}` (related to "{label}")' for key, label in required_data.items()])

        return f"""
You are a specialized data extraction bot for medical documents. Your task is to analyze the provided images of a patient's referral document and extract specific pieces of information.

**1. Goal:**
Find and extract the values for the following data points. The text in parentheses is the label from the form where this data will be placed, use it as a hint to find the correct value.

**Data to Extract:**
{data_points_str}

**2. Rules:**
- Read the entire document carefully to find the most accurate information.
- Provide the data in the requested JSON format.
- If you cannot find a value for a specific field, return `null` for that key. Do not make up information.
- For dates, use the format `MM/DD/YYYY`.
- For clinical questions (those starting with `clinical_question_`), answer based on the document content with "Yes", "No", or a specific value if requested.

**3. Output Format:**
You MUST return your findings as a single JSON object.

**Example JSON Output:**
```json
{{
  "extracted_data": {{
    "patient_last_name": "Chen",
    "patient_first_name": "Amy",
    "patient_dob": "05/23/1983",
    "prescriber_npi": "**********",
    "clinical_question_is_patient_over_18": "Yes"
  }}
}}
```

Now, analyze the document and return the extracted data.
"""

class FlatFormFillingAgent:
    """
    Fills a flat PDF form deterministically using a completed schema and extracted data.
    This agent uses precise coordinates and does NOT involve AI for placement.
    """
    def fill_form(self,
                  schema: FormSchema,
                  extracted_data: ExtractedData,
                  output_path: Path,
                  base_form_path: Path):
        
        print(f"🖋️ Activating Deterministic Form Filling Agent.")
        if not extracted_data.data:
            print("⚠️ No data extracted. Aborting form filling.")
            return

        doc = fitz.open(base_form_path)
        
        # Map semantic purposes to their fields for quick lookup
        purpose_to_field_map: Dict[str, FormField] = {
            field.semantic_purpose: field
            for field in schema.fields if field.semantic_purpose
        }
        
        fields_filled = 0
        for purpose, value in extracted_data.data.items():
            if value is None or value == "null":
                continue

            if purpose in purpose_to_field_map:
                field = purpose_to_field_map[purpose]
                page = doc[field.page_num]
                
                # Convert value to string for insertion
                insert_text = str(value)

                if field.type == "char_boxes":
                    self._fill_char_boxes(page, field, insert_text)
                    fields_filled += 1
                elif field.type == "underline":
                    self._fill_underline(page, field, insert_text)
                    fields_filled += 1
                elif field.type == "checkbox":
                    # Handle boolean-like values for checkboxes
                    if insert_text.lower() in ["yes", "true", "x"]:
                         self._fill_checkbox(page, field)
                         fields_filled += 1
                else:
                    print(f"⚠️ Unknown field type '{field.type}' for purpose '{purpose}'.")

        # Save the document
        output_path.parent.mkdir(parents=True, exist_ok=True)
        doc.save(str(output_path), garbage=4, deflate=True, clean=True)
        doc.close()
        print(f"✅ Form filling complete. {fields_filled} fields filled. Saved to: {output_path}")

    def _get_absolute_coords(self, page: fitz.Page, normalized_coords: List[float]) -> fitz.Rect:
        """Converts normalized coordinates to absolute PDF coordinates."""
        page_width = page.rect.width
        page_height = page.rect.height
        x1 = normalized_coords[0] * page_width
        y1 = normalized_coords[1] * page_height
        x2 = normalized_coords[2] * page_width
        y2 = normalized_coords[3] * page_height
        return fitz.Rect(x1, y1, x2, y2)

    def _fill_char_boxes(self, page: fitz.Page, field: FormField, text: str):
        """Fills a sequence of character boxes one character at a time."""
        # Clean the text to only include characters that can be placed
        text_to_place = re.sub(r'[^a-zA-Z0-9]', '', text).upper()
        
        for i, char_coords in enumerate(field.coordinates):
            if i < len(text_to_place):
                char = text_to_place[i]
                rect = self._get_absolute_coords(page, char_coords)
                # Adjust font size to fit the box height, with a small margin
                font_size = rect.height * 0.75
                # Insert character centered in the box
                page.insert_text(
                    (rect.x0, rect.y1), # Use bottom-left for insertion point
                    char,
                    fontsize=font_size,
                    fontname="helv", # Use a standard font
                    align=fitz.TEXT_ALIGN_CENTER
                )

    def _fill_underline(self, page: fitz.Page, field: FormField, text: str):
        """Fills a field denoted by an underline."""
        if not field.coordinates:
            return
        
        rect = self._get_absolute_coords(page, field.coordinates[0])
        # Use a slightly smaller font size than the underline's "height"
        font_size = max(rect.height * 0.8, 8) # Minimum font size of 8
        # Insert text at the beginning of the line
        page.insert_text(
            (rect.x0, rect.y1),
            text,
            fontsize=font_size,
            fontname="helv"
        )

    def _fill_checkbox(self, page: fitz.Page, field: FormField):
        """Draws a checkmark inside a checkbox."""
        if not field.coordinates:
            return
        
        rect = self._get_absolute_coords(page, field.coordinates[0])
        # Draw two lines to form an 'X' or a checkmark
        # A simple 'X' is often sufficient and easier
        shape = page.new_shape()
        shape.draw_line(rect.tl, rect.br)
        shape.draw_line(rect.bl, rect.tr)
        shape.finish(color=(0, 0, 0), width=0.5)
        shape.commit()


class MandolinPipeline:
    """
    Orchestrates the entire PA form processing workflow using the new
    schema-first, decoupled agent architecture.
    """
    def __init__(self):
        # Main system components based on the new architecture
        self.harvester = SchemaHarvesterAgent()
        self.mapper = SemanticMapperAgent()
        self.extractor = DataExtractionAgent()
        self.filler = FlatFormFillingAgent()
        self.schema_cache: Dict[str, FormSchema] = {}

    def get_cached_schema(self, form_path: Path) -> Optional[FormSchema]:
        """Checks for a pre-harvested schema JSON file."""
        cache_file = form_path.with_suffix('.schema.json')
        if cache_file.exists():
            print(f"Found cached schema: {cache_file}")
            try:
                with open(cache_file, 'r') as f:
                    data = json.load(f)
                    fields = [FormField(**field_data) for field_data in data.get("fields", [])]
                    return FormSchema(fields=fields)
            except Exception as e:
                print(f"Warning: Could not load cached schema. Re-harvesting. Reason: {e}")
        return None

    def save_schema_to_cache(self, form_path: Path, schema: FormSchema):
        """Saves a harvested schema to a JSON file."""
        cache_file = form_path.with_suffix('.schema.json')
        try:
            with open(cache_file, 'w') as f:
                json.dump(schema.to_dict(), f, indent=2)
            print(f"Schema saved to cache: {cache_file}")
        except Exception as e:
            print(f"Error saving schema to cache: {e}")


    def process_pa(self, patient_name: str, referral_path: Path, pa_form_path: Path, output_dir: Path):
        """
        Executes the full, robust pipeline for processing a single PA request.
        """
        print("\n" + "="*50)
        print(f"🚀 Starting Mandolin Pipeline for Patient: {patient_name}")
        print(f"   Referral Doc: {referral_path.name}")
        print(f"   PA Form: {pa_form_path.name}")
        print("="*50 + "\n")

        # --- Phase 1: Schema Harvesting (with Caching) ---
        schema = self.get_cached_schema(pa_form_path)
        if not schema:
            schema = self.harvester.harvest_schema(pa_form_path)
            if not schema.fields:
                print("❌ Pipeline halted: Schema harvesting failed.")
                return
            # --- Phase 2: Semantic Mapping ---
            # This only needs to be done once when the schema is first created.
            schema = self.mapper.map_semantics(schema)
            self.save_schema_to_cache(pa_form_path, schema)

        # --- Phase 3: Targeted Data Extraction ---
        extracted_data = self.extractor.extract_data(referral_path, schema)
        if not extracted_data.data:
            print("❌ Pipeline halted: Data extraction failed or yielded no data.")
            return

        # --- Phase 4: Deterministic Form Filling ---
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"{patient_name.replace(' ', '_')}_PA_{timestamp}.pdf"
        output_path = output_dir / output_filename

        self.filler.fill_form(
            schema=schema,
            extracted_data=extracted_data,
            output_path=output_path,
            base_form_path=pa_form_path
        )

        print("\n" + "="*50)
        print("✅ Mandolin Pipeline finished successfully.")
        print("="*50 + "\n")


def find_files(directory: Path, name_contains: str) -> Optional[Path]:
    """Finds the first file in a directory whose name contains a string."""
    for item in directory.iterdir():
        if item.is_file() and name_contains in item.name:
            return item
    return None


def main():
    """Main function to run the Mandolin PA processing pipeline."""
    # Define directories
    base_dir = Path(__file__).parent
    input_dir = base_dir / "pa_forms" / "patient_documents"
    output_dir = base_dir / "pa_forms" / "completed"

    # Find the necessary files
    # In a real system, this would come from a queue or database
    referral_doc_path = find_files(input_dir, "Referral")
    pa_form_path = find_files(input_dir, "FORM")

    if not referral_doc_path or not pa_form_path:
        print("❌ Error: Could not find required 'Referral' and 'FORM' files in the input directory.")
        return

    # Patient name could be extracted or passed as an argument
    patient_name = "Amy Chen" 

    # Initialize and run the pipeline
    pipeline = MandolinPipeline()
    pipeline.process_pa(
        patient_name=patient_name,
        referral_path=referral_doc_path,
        pa_form_path=pa_form_path,
        output_dir=output_dir
    )

if __name__ == "__main__":
    main() 